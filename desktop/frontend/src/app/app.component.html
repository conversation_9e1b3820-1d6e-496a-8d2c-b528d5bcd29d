<!-- Modern App Layout with Bottom Tabs -->
<div class="app-container">
  <!-- Main Content with Bottom Tabs -->
  <div class="content-container">
    <mat-tab-group
      [selectedIndex]="getSelectedTabIndex()"
      (selectedIndexChange)="onTabChange($event)"
      headerPosition="below"
      animationDuration="300ms"
    >
      <!-- Operations Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>dashboard</mat-icon>
          <span>Operations</span>
        </ng-template>
        <app-home></app-home>
      </mat-tab>

      <!-- Profiles Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>folder_shared</mat-icon>
          <span>Profiles</span>
        </ng-template>
        <app-profiles
          *ngIf="(navigationService.currentState$ | async)?.page === 'profiles'"
        ></app-profiles>
        <app-profile-edit
          *ngIf="
            (navigationService.currentState$ | async)?.page === 'profile-edit'
          "
        ></app-profile-edit>
      </mat-tab>

      <!-- Remotes Tab -->
      <mat-tab>
        <ng-template mat-tab-label>
          <mat-icon>cloud_queue</mat-icon>
          <span>Remotes</span>
        </ng-template>
        <app-remotes></app-remotes>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
