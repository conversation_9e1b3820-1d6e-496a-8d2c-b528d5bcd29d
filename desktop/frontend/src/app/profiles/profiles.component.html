<!-- Profiles List -->
<div class="page-container content-spacing">
  <mat-card
    *ngIf="(appService.configInfo$ | async)?.profiles?.length; else noProfiles"
    class="card-spacing"
  >
    <mat-card-header>
      <mat-card-title>
        <mat-icon>folder_shared</mat-icon>
        <span>Sync Profiles</span>
      </mat-card-title>
      <mat-card-subtitle>
        {{ (appService.configInfo$ | async)?.profiles?.length }} profile(s)
        configured
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <mat-list>
        <mat-list-item
          *ngFor="
            let profile of (appService.configInfo$ | async)?.profiles;
            let idx = index
          "
          (click)="editProfile(idx)"
          class="profile-item"
        >
          <div matListItemTitle>{{ profile.name || "Untitled Profile" }}</div>
          <div matListItemLine>{{ getProfileDescription(profile) }}</div>
          <div matListItemMeta>
            <button
              mat-icon-button
              color="warn"
              (click)="removeProfile(idx); $event.stopPropagation()"
              matTooltip="Delete profile"
            >
              <mat-icon>delete</mat-icon>
            </button>
          </div>
        </mat-list-item>
      </mat-list>
    </mat-card-content>
  </mat-card>
</div>

<!-- No Profiles State -->
<ng-template #noProfiles>
  <div class="page-container">
    <mat-card class="card-spacing">
      <mat-card-header>
        <mat-icon mat-card-avatar>folder_off</mat-icon>
        <mat-card-title>No Profiles Found</mat-card-title>
        <mat-card-subtitle>
          Create your first sync profile to get started
        </mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <p>
          Profiles allow you to configure different sync settings for various
          directories and remotes.
        </p>
      </mat-card-content>
    </mat-card>
  </div>
</ng-template>

<!-- Floating Action Button -->
<button
  mat-fab
  color="primary"
  (click)="addProfile()"
  class="fab-button"
  matTooltip="Add Profile"
>
  <mat-icon>add</mat-icon>
</button>
