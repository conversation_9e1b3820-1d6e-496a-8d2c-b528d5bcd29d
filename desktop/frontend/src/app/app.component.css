.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.content-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.spacer {
  flex: 1 1 auto;
}

/* Bottom tabs styling */
::ng-deep .mat-mdc-tab-group {
  height: 100%;
  display: flex;
  flex-direction: column;
}

::ng-deep .mat-mdc-tab-body-wrapper {
  flex: 1;
  overflow: auto;
}

::ng-deep .mat-mdc-tab-header {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}

::ng-deep .mat-mdc-tab-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-height: 64px;
  padding: 8px 12px;
}

::ng-deep .mat-mdc-tab-label mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

::ng-deep .mat-mdc-tab-label span {
  font-size: 12px;
  font-weight: 500;
}

/* Tab styling (permanent dark mode) */
::ng-deep .mat-mdc-tab-header {
  border-top: 1px solid rgba(255, 255, 255, 0.12);
  background-color: var(--surface-color);
}

::ng-deep .mat-mdc-tab-body-wrapper {
  background-color: var(--background-color);
}

/* Active tab background for bottom navigation */
::ng-deep .mat-mdc-tab-label.mdc-tab--active {
  background-color: var(--surface-color) !important;
  border-radius: 8px 8px 0 0;
}

::ng-deep .mat-mdc-tab-label:not(.mdc-tab--active) {
  background-color: transparent;
}

::ng-deep .mat-mdc-tab-label {
  transition: background-color 0.3s ease;
}
